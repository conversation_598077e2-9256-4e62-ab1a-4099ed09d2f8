{"flowContents": {"identifier": "optimized-device-flow", "name": "优化版设备数据处理流程", "description": "使用连接池和缓存优化的设备数据处理流程", "processors": [{"identifier": "kafka-consumer", "name": "ConsumeKafka_2_6", "type": "org.apache.nifi.processors.kafka.pubsub.ConsumeKafka_2_6", "position": {"x": 100, "y": 100}, "config": {"properties": {"bootstrap.servers": "localhost:9092", "topic": "device-monitoring", "group.id": "nifi-device-consumer", "auto.offset.reset": "latest", "security.protocol": "SASL_PLAINTEXT", "sasl.mechanism": "PLAIN", "sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"admin\" password=\"adminpassword\";", "max.poll.records": "1000"}}}, {"identifier": "extract-json-fields", "name": "Evaluate<PERSON>sonPath", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 400, "y": 100}, "config": {"properties": {"Destination": "flowfile-attribute", "ts": "$.ts", "dev_id": "$.dev_id", "factory_id": "$.factory_id", "prop_a": "$.prop_a", "prop_b": "$.prop_b", "prop_c": "$.prop_c", "prop_d": "$.prop_d"}}}, {"identifier": "fetch-device-cache", "name": "FetchDistributedMapCache - 设备信息", "type": "org.apache.nifi.processors.standard.FetchDistributedMapCache", "position": {"x": 700, "y": 50}, "config": {"properties": {"Distributed Cache Service": "redis-distributed-cache", "Cache Entry Identifier": "device:${dev_id}", "Put Cache Value In Attribute": "device_info_json"}}}, {"identifier": "optimized-data-processor", "name": "ExecuteScript - 优化数据处理", "type": "org.apache.nifi.processors.script.ExecuteScript", "position": {"x": 1000, "y": 100}, "config": {"properties": {"Script Engine": "python", "Script Body": "import json\nimport time\nfrom java.util.concurrent import ConcurrentHashMap\n\n# 全局缓存（进程级别）\nif 'global_cache' not in globals():\n    global_cache = ConcurrentHashMap()\n    cache_ttl = ConcurrentHashMap()  # TTL缓存\n\ndef get_from_cache_or_redis(cache_key, redis_key, cache_service):\n    \"\"\"从缓存获取数据，缓存未命中时从Redis获取\"\"\"\n    current_time = int(time.time())\n    \n    # 检查本地缓存\n    if cache_key in global_cache:\n        if cache_key in cache_ttl and cache_ttl[cache_key] > current_time:\n            return global_cache[cache_key]\n        else:\n            # 缓存过期，清理\n            global_cache.remove(cache_key)\n            if cache_key in cache_ttl:\n                cache_ttl.remove(cache_key)\n    \n    # 从Redis获取\n    try:\n        data = cache_service.get(redis_key, session)\n        if data:\n            # 存入本地缓存，TTL 5分钟\n            global_cache[cache_key] = data\n            cache_ttl[cache_key] = current_time + 300\n            return data\n    except Exception as e:\n        session.log.error(f'Redis查询失败: {e}')\n    \n    return None\n\n# 获取FlowFile\nflowFile = session.get()\nif flowFile is not None:\n    try:\n        dev_id = flowFile.getAttribute('dev_id')\n        \n        # 获取缓存服务\n        cache_service = context.getProperty('Distributed Cache Service').asControllerService()\n        \n        # 批量获取配置信息（使用本地缓存）\n        device_info = get_from_cache_or_redis(f'device_{dev_id}', f'device:{dev_id}', cache_service)\n        \n        if device_info:\n            device_data = json.loads(device_info)\n            dev_group_id = device_data.get('dev_group_id')\n            dev_product_id = device_data.get('dev_product_id')\n            \n            # 并行获取分组和产品信息\n            group_info = get_from_cache_or_redis(f'group_{dev_group_id}', f'group:{dev_group_id}', cache_service)\n            product_info = get_from_cache_or_redis(f'product_{dev_product_id}', f'product:{dev_product_id}', cache_service)\n            \n            # 设置属性\n            flowFile = session.putAttribute(flowFile, 'dev_name', device_data.get('dev_name', ''))\n            flowFile = session.putAttribute(flowFile, 'dev_group_id', dev_group_id or '')\n            flowFile = session.putAttribute(flowFile, 'dev_product_id', dev_product_id or '')\n            \n            if group_info:\n                group_data = json.loads(group_info)\n                flowFile = session.putAttribute(flowFile, 'dev_group_name', group_data.get('dev_group_name', ''))\n            \n            if product_info:\n                flowFile = session.putAttribute(flowFile, 'prop_items', product_info)\n        \n        session.transfer(flowFile, REL_SUCCESS)\n        \n    except Exception as e:\n        session.log.error(f'处理失败: {e}')\n        session.transfer(flowFile, REL_FAILURE)\nelse:\n    session.transfer(flowFile, REL_FAILURE)", "Module Directory": "/opt/nifi/nifi-current/lib"}}}], "controllerServices": [{"identifier": "redis-distributed-cache", "name": "DistributedMapCacheClientService - Redis", "type": "org.apache.nifi.redis.service.RedisDistributedMapCacheClientService", "properties": {"Redis Connection Pool": "redis-connection-pool", "Communications Timeout": "30 secs"}}, {"identifier": "redis-connection-pool", "name": "RedisConnectionPoolService", "type": "org.apache.nifi.redis.service.RedisConnectionPoolService", "properties": {"Connection String": "localhost:6379", "Database Index": "0", "Communication Timeout": "10 secs", "Pool - Max Total": "20", "Pool - Max Idle": "10", "Pool - Min Idle": "2", "Pool - Block When Exhausted": "true", "Pool - Max Wait Time": "10 secs"}}, {"identifier": "postgresql-connection-pool", "name": "DBCPConnectionPool - PostgreSQL", "type": "org.apache.nifi.dbcp.DBCPConnectionPool", "properties": {"Database Connection URL": "**************************************************", "Database Driver Class Name": "org.postgresql.Driver", "Database User": "postgres", "Password": "password", "Max Total Connections": "20", "Max Idle Connections": "10", "Min Idle Connections": "2"}}]}}