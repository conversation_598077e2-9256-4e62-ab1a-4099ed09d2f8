# 设备管理数据接入处理流程

## 概述

这个 Apache NiFi 流程用于处理设备监测数据，从 Kafka 消费数据，查询 Redis 中的配置信息，然后将处理后的数据写入 PostgreSQL 和 TDengine 数据库。

## 流程架构

```
Kafka消息 → JSON解析 → Redis查询(设备信息) → Redis查询(分组信息) → Redis查询(属性信息) 
    ↓
属性拆分 → 并行处理:
    ├── PostgreSQL点位定义表
    ├── PostgreSQL实时数据表  
    └── TDengine历史数据表
```

## 前置条件

### 1. 环境要求
- Apache NiFi 2.4.0
- Apache NiFi Registry 2.4.0
- Kafka 集群（支持 SASL 认证）
- Redis 服务器
- PostgreSQL 数据库
- TDengine 数据库

### 2. 数据库准备

#### PostgreSQL 表结构
```sql
-- 设备点位定义表
CREATE TABLE device_point_definition (
    point_id VARCHAR(100) PRIMARY KEY,
    point_name VARCHAR(200),
    factory_id VARCHAR(50),
    system_id VARCHAR(50),
    data_type VARCHAR(20)
);

-- 设备点位实时数据表
CREATE TABLE device_realtime_data (
    point_id VARCHAR(100) PRIMARY KEY,
    ts TIMESTAMP,
    value TEXT
);
```

#### TDengine 表结构
```sql
-- 创建数据库
CREATE DATABASE device_monitoring;

-- 使用数据库
USE device_monitoring;

-- 创建超级表
CREATE STABLE device_history_template (
    ts TIMESTAMP,
    value NCHAR(200)
) TAGS (
    point_id NCHAR(100),
    point_name NCHAR(200),
    factory_id NCHAR(50),
    system_id NCHAR(50),
    data_type NCHAR(20)
);
```

### 3. Redis 数据准备

#### 设备信息
```bash
# 设备信息 key: device:{dev_id}
redis-cli SET "device:D001" '{"dev_id":"D001","dev_name":"设备1","dev_group_id":"G001","dev_product_id":"P001"}'
```

#### 设备分组信息
```bash
# 设备分组信息 key: group:{dev_group_id}
redis-cli SET "group:G001" '{"dev_group_id":"G001","dev_group_name":"设备分组1"}'
```

#### 设备属性信息
```bash
# 设备属性信息 key: product:{dev_product_id}
redis-cli SET "product:P001" '{
  "dev_product_id":"P001",
  "dev_product_name":"设备产品1",
  "prop_items":{
    "prop_a":{"prop_id":"A001","prop_name":"文本属性a","data_type":"string"},
    "prop_b":{"prop_id":"B001","prop_name":"浮点型属性b","data_type":"double"},
    "prop_c":{"prop_id":"C001","prop_name":"整型属性c","data_type":"int"},
    "prop_d":{"prop_id":"D001","prop_name":"布尔型属性d","data_type":"boolean"}
  }
}'
```

## 安装和配置

### 1. 导入流程
1. 打开 NiFi Registry Web UI
2. 创建新的 Bucket（如果还没有）
3. 导入 `device-data-processing-flow.json` 文件

### 2. 配置修改

#### Kafka 配置
在 ConsumeKafka_2_6 处理器中修改以下配置：
- `bootstrap.servers`: 您的 Kafka 集群地址
- `topic`: 您的设备监测数据主题名称
- `sasl.jaas.config`: 修改用户名和密码

#### 数据库连接配置
修改控制器服务中的数据库连接信息：
- PostgreSQL 连接池：修改数据库 URL、用户名、密码
- TDengine 连接池：修改数据库 URL、用户名、密码

#### Redis 配置
在 ExecuteScript 处理器中修改 Redis 连接信息：
```python
r = redis.Redis(host='your-redis-host', port=6379, db=0)
```

### 3. 依赖库安装

确保 NiFi 环境中安装了以下 JAR 包：
- PostgreSQL JDBC 驱动：`postgresql-42.7.0.jar`
- TDengine JDBC 驱动：`taos-jdbcdriver-3.2.0.jar`
- Redis Python 客户端：在 NiFi 的 Python 环境中安装 `redis` 包

## 数据流说明

### 输入数据格式
```json
{
  "ts": "2023-01-01 00:00:00",
  "dev_id": "D001",
  "factory_id": "F001",
  "prop_a": "测试数据1",
  "prop_b": 12.3545,
  "prop_c": 1,
  "prop_d": false
}
```

### 处理逻辑
1. **消费 Kafka 消息**：从指定主题消费设备监测数据
2. **解析 JSON**：提取消息中的各个字段
3. **查询配置信息**：
   - 根据 `dev_id` 查询设备基本信息
   - 根据 `dev_group_id` 查询设备分组信息
   - 根据 `dev_product_id` 查询设备属性定义
4. **数据拆分**：将每个属性拆分为独立的数据点
5. **生成关键信息**：
   - `point_id`: `{dev_id}_{prop_id}`
   - `point_name`: `{dev_name}_{prop_name}`
   - `system_id`: `001_{dev_group_id}`
6. **数据写入**：
   - PostgreSQL 点位定义表（去重插入）
   - PostgreSQL 实时数据表（更新插入）
   - TDengine 历史数据表（时序插入）

## 监控和维护

### 关键指标监控
- Kafka 消费延迟
- Redis 查询响应时间
- 数据库写入成功率
- 流程处理吞吐量

### 故障排查
1. 检查 Kafka 连接和认证配置
2. 验证 Redis 中的配置数据完整性
3. 确认数据库连接和表结构正确性
4. 查看 NiFi 处理器的错误日志

## 扩展建议

1. **性能优化**：
   - 增加 Kafka 消费者并发数
   - 使用批量写入提高数据库性能
   - 考虑使用 Redis 集群提高查询性能

2. **容错处理**：
   - 添加重试机制
   - 实现死信队列处理
   - 增加数据校验逻辑

3. **监控告警**：
   - 集成 Prometheus 监控
   - 设置关键指标告警
   - 实现流程健康检查
