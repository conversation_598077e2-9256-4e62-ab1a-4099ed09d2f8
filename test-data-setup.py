#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备数据处理流程测试数据准备脚本
用于向 Redis 和 Kafka 写入测试数据
"""

import json
import redis
import time
from kafka import KafkaProducer
from datetime import datetime

# 配置信息
REDIS_CONFIG = {
    'host': 'localhost',
    'port': 6379,
    'db': 0
}

KAFKA_CONFIG = {
    'bootstrap_servers': ['localhost:9092'],
    'security_protocol': 'SASL_PLAINTEXT',
    'sasl_mechanism': 'PLAIN',
    'sasl_plain_username': 'admin',
    'sasl_plain_password': 'adminpassword',
    'value_serializer': lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
}

KAFKA_TOPIC = 'device-monitoring'

def setup_redis_data():
    """设置 Redis 测试数据"""
    print("正在设置 Redis 测试数据...")
    
    r = redis.Redis(**REDIS_CONFIG)
    
    # 设备信息
    devices = [
        {
            "dev_id": "D001",
            "dev_name": "温度传感器1",
            "dev_group_id": "G001",
            "dev_product_id": "P001"
        },
        {
            "dev_id": "D002", 
            "dev_name": "湿度传感器1",
            "dev_group_id": "G001",
            "dev_product_id": "P002"
        },
        {
            "dev_id": "D003",
            "dev_name": "压力传感器1", 
            "dev_group_id": "G002",
            "dev_product_id": "P003"
        }
    ]
    
    for device in devices:
        key = f"device:{device['dev_id']}"
        r.set(key, json.dumps(device, ensure_ascii=False))
        print(f"设置设备信息: {key}")
    
    # 设备分组信息
    groups = [
        {
            "dev_group_id": "G001",
            "dev_group_name": "环境监测组"
        },
        {
            "dev_group_id": "G002", 
            "dev_group_name": "压力监测组"
        }
    ]
    
    for group in groups:
        key = f"group:{group['dev_group_id']}"
        r.set(key, json.dumps(group, ensure_ascii=False))
        print(f"设置分组信息: {key}")
    
    # 设备属性信息
    products = [
        {
            "dev_product_id": "P001",
            "dev_product_name": "温度传感器产品",
            "prop_items": {
                "prop_a": {"prop_id": "A001", "prop_name": "温度值", "data_type": "double"},
                "prop_b": {"prop_id": "B001", "prop_name": "状态", "data_type": "string"},
                "prop_c": {"prop_id": "C001", "prop_name": "电池电量", "data_type": "int"},
                "prop_d": {"prop_id": "D001", "prop_name": "在线状态", "data_type": "boolean"}
            }
        },
        {
            "dev_product_id": "P002",
            "dev_product_name": "湿度传感器产品", 
            "prop_items": {
                "prop_a": {"prop_id": "A002", "prop_name": "湿度值", "data_type": "double"},
                "prop_b": {"prop_id": "B002", "prop_name": "状态", "data_type": "string"},
                "prop_c": {"prop_id": "C002", "prop_name": "信号强度", "data_type": "int"},
                "prop_d": {"prop_id": "D002", "prop_name": "在线状态", "data_type": "boolean"}
            }
        },
        {
            "dev_product_id": "P003",
            "dev_product_name": "压力传感器产品",
            "prop_items": {
                "prop_a": {"prop_id": "A003", "prop_name": "压力值", "data_type": "double"},
                "prop_b": {"prop_id": "B003", "prop_name": "单位", "data_type": "string"},
                "prop_c": {"prop_id": "C003", "prop_name": "精度等级", "data_type": "int"},
                "prop_d": {"prop_id": "D003", "prop_name": "校准状态", "data_type": "boolean"}
            }
        }
    ]
    
    for product in products:
        key = f"product:{product['dev_product_id']}"
        r.set(key, json.dumps(product, ensure_ascii=False))
        print(f"设置产品信息: {key}")
    
    print("Redis 测试数据设置完成!")

def send_test_messages():
    """发送测试消息到 Kafka"""
    print("正在发送测试消息到 Kafka...")
    
    producer = KafkaProducer(**KAFKA_CONFIG)
    
    # 测试消息
    test_messages = [
        {
            "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "dev_id": "D001",
            "factory_id": "F001",
            "prop_a": 25.6,
            "prop_b": "正常",
            "prop_c": 85,
            "prop_d": True
        },
        {
            "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "dev_id": "D002", 
            "factory_id": "F001",
            "prop_a": 65.2,
            "prop_b": "正常",
            "prop_c": 78,
            "prop_d": True
        },
        {
            "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "dev_id": "D003",
            "factory_id": "F002", 
            "prop_a": 1.25,
            "prop_b": "MPa",
            "prop_c": 1,
            "prop_d": True
        }
    ]
    
    for i, message in enumerate(test_messages):
        try:
            future = producer.send(KAFKA_TOPIC, message)
            result = future.get(timeout=10)
            print(f"消息 {i+1} 发送成功: {message['dev_id']} - {result}")
            time.sleep(1)  # 间隔1秒发送
        except Exception as e:
            print(f"消息 {i+1} 发送失败: {e}")
    
    producer.flush()
    producer.close()
    print("测试消息发送完成!")

def continuous_send_messages(interval=5, count=10):
    """持续发送测试消息"""
    print(f"开始持续发送测试消息，间隔 {interval} 秒，共 {count} 次...")
    
    producer = KafkaProducer(**KAFKA_CONFIG)
    
    import random
    
    devices = ["D001", "D002", "D003"]
    factories = ["F001", "F002"]
    
    for i in range(count):
        dev_id = random.choice(devices)
        factory_id = random.choice(factories)
        
        # 根据设备类型生成不同的测试数据
        if dev_id == "D001":  # 温度传感器
            message = {
                "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "dev_id": dev_id,
                "factory_id": factory_id,
                "prop_a": round(random.uniform(20.0, 30.0), 2),
                "prop_b": random.choice(["正常", "告警"]),
                "prop_c": random.randint(70, 100),
                "prop_d": random.choice([True, False])
            }
        elif dev_id == "D002":  # 湿度传感器
            message = {
                "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "dev_id": dev_id,
                "factory_id": factory_id,
                "prop_a": round(random.uniform(40.0, 80.0), 2),
                "prop_b": random.choice(["正常", "异常"]),
                "prop_c": random.randint(60, 90),
                "prop_d": random.choice([True, False])
            }
        else:  # 压力传感器
            message = {
                "ts": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "dev_id": dev_id,
                "factory_id": factory_id,
                "prop_a": round(random.uniform(0.5, 2.0), 3),
                "prop_b": "MPa",
                "prop_c": random.randint(1, 3),
                "prop_d": random.choice([True, False])
            }
        
        try:
            future = producer.send(KAFKA_TOPIC, message)
            result = future.get(timeout=10)
            print(f"[{i+1}/{count}] 消息发送成功: {dev_id} - {message['prop_a']}")
        except Exception as e:
            print(f"[{i+1}/{count}] 消息发送失败: {e}")
        
        time.sleep(interval)
    
    producer.flush()
    producer.close()
    print("持续发送完成!")

if __name__ == "__main__":
    print("设备数据处理流程测试数据准备")
    print("=" * 50)
    
    try:
        # 1. 设置 Redis 数据
        setup_redis_data()
        print()
        
        # 2. 发送测试消息
        send_test_messages()
        print()
        
        # 3. 询问是否持续发送
        choice = input("是否要持续发送测试消息？(y/n): ").lower()
        if choice == 'y':
            interval = int(input("发送间隔（秒，默认5）: ") or "5")
            count = int(input("发送次数（默认10）: ") or "10")
            continuous_send_messages(interval, count)
        
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
