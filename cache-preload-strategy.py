#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis 配置缓存预加载策略
用于减少实时查询压力，提高处理性能
"""

import json
import redis
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional

class DeviceConfigCache:
    """设备配置缓存管理器"""
    
    def __init__(self, redis_config: Dict[str, Any], cache_ttl: int = 300):
        """
        初始化缓存管理器
        
        Args:
            redis_config: Redis连接配置
            cache_ttl: 缓存TTL（秒），默认5分钟
        """
        self.redis_pool = redis.ConnectionPool(**redis_config)
        self.cache_ttl = cache_ttl
        self.local_cache = {}
        self.cache_timestamps = {}
        self.lock = threading.RLock()
        
        # 启动后台刷新线程
        self.refresh_thread = threading.Thread(target=self._background_refresh, daemon=True)
        self.refresh_thread.start()
    
    def get_device_config(self, dev_id: str) -> Optional[Dict[str, Any]]:
        """
        获取设备完整配置信息（设备+分组+属性）
        
        Args:
            dev_id: 设备ID
            
        Returns:
            完整的设备配置信息，如果获取失败返回None
        """
        cache_key = f"full_config_{dev_id}"
        
        # 检查本地缓存
        with self.lock:
            if self._is_cache_valid(cache_key):
                return self.local_cache[cache_key]
        
        # 缓存未命中，从Redis获取
        config = self._fetch_full_config_from_redis(dev_id)
        
        if config:
            with self.lock:
                self.local_cache[cache_key] = config
                self.cache_timestamps[cache_key] = time.time()
        
        return config
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.local_cache:
            return False
        
        if cache_key not in self.cache_timestamps:
            return False
        
        return (time.time() - self.cache_timestamps[cache_key]) < self.cache_ttl
    
    def _fetch_full_config_from_redis(self, dev_id: str) -> Optional[Dict[str, Any]]:
        """从Redis获取完整配置信息"""
        try:
            r = redis.Redis(connection_pool=self.redis_pool)
            
            # 获取设备信息
            device_info = r.get(f"device:{dev_id}")
            if not device_info:
                return None
            
            device_data = json.loads(device_info)
            dev_group_id = device_data.get('dev_group_id')
            dev_product_id = device_data.get('dev_product_id')
            
            # 并行获取分组和产品信息
            with ThreadPoolExecutor(max_workers=2) as executor:
                group_future = executor.submit(r.get, f"group:{dev_group_id}")
                product_future = executor.submit(r.get, f"product:{dev_product_id}")
                
                group_info = group_future.result()
                product_info = product_future.result()
            
            # 组装完整配置
            full_config = {
                'device': device_data,
                'group': json.loads(group_info) if group_info else {},
                'product': json.loads(product_info) if product_info else {}
            }
            
            return full_config
            
        except Exception as e:
            print(f"Redis查询失败: {e}")
            return None
    
    def preload_configs(self, device_ids: list):
        """预加载指定设备的配置信息"""
        print(f"开始预加载 {len(device_ids)} 个设备的配置信息...")
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for dev_id in device_ids:
                future = executor.submit(self.get_device_config, dev_id)
                futures.append((dev_id, future))
            
            success_count = 0
            for dev_id, future in futures:
                try:
                    config = future.result(timeout=10)
                    if config:
                        success_count += 1
                        print(f"✓ 预加载成功: {dev_id}")
                    else:
                        print(f"✗ 预加载失败: {dev_id} (配置不存在)")
                except Exception as e:
                    print(f"✗ 预加载失败: {dev_id} ({e})")
        
        print(f"预加载完成: {success_count}/{len(device_ids)} 成功")
    
    def _background_refresh(self):
        """后台刷新过期缓存"""
        while True:
            try:
                time.sleep(60)  # 每分钟检查一次
                
                with self.lock:
                    current_time = time.time()
                    expired_keys = []
                    
                    for cache_key, timestamp in self.cache_timestamps.items():
                        if (current_time - timestamp) > (self.cache_ttl * 0.8):  # 80%过期时刷新
                            expired_keys.append(cache_key)
                    
                    # 刷新过期缓存
                    for cache_key in expired_keys:
                        if cache_key.startswith('full_config_'):
                            dev_id = cache_key.replace('full_config_', '')
                            threading.Thread(
                                target=self._refresh_single_config,
                                args=(dev_id,),
                                daemon=True
                            ).start()
                            
            except Exception as e:
                print(f"后台刷新异常: {e}")
    
    def _refresh_single_config(self, dev_id: str):
        """刷新单个设备配置"""
        try:
            config = self._fetch_full_config_from_redis(dev_id)
            if config:
                cache_key = f"full_config_{dev_id}"
                with self.lock:
                    self.local_cache[cache_key] = config
                    self.cache_timestamps[cache_key] = time.time()
        except Exception as e:
            print(f"刷新配置失败 {dev_id}: {e}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            return {
                'cache_size': len(self.local_cache),
                'cache_keys': list(self.local_cache.keys()),
                'oldest_cache': min(self.cache_timestamps.values()) if self.cache_timestamps else None,
                'newest_cache': max(self.cache_timestamps.values()) if self.cache_timestamps else None
            }

# NiFi ExecuteScript 中使用的优化代码
NIFI_OPTIMIZED_SCRIPT = '''
import json
import time
from java.util.concurrent import ConcurrentHashMap

# 全局缓存实例（进程级别）
if 'device_cache_manager' not in globals():
    device_cache_manager = None
    global_cache = ConcurrentHashMap()
    cache_timestamps = ConcurrentHashMap()

def get_or_create_cache_manager():
    """获取或创建缓存管理器"""
    global device_cache_manager
    if device_cache_manager is None:
        # 这里应该从NiFi的Controller Service获取Redis连接
        # device_cache_manager = DeviceConfigCache(redis_config)
        pass
    return device_cache_manager

def process_device_message(flowFile, session):
    """处理设备消息的优化版本"""
    try:
        dev_id = flowFile.getAttribute('dev_id')
        cache_key = f"full_config_{dev_id}"
        current_time = int(time.time())
        
        # 检查本地缓存
        if cache_key in global_cache:
            cache_time = cache_timestamps.get(cache_key, 0)
            if (current_time - cache_time) < 300:  # 5分钟TTL
                config = global_cache[cache_key]
                return apply_config_to_flowfile(flowFile, session, config)
        
        # 缓存未命中，从Redis获取（这里应该使用DistributedMapCache服务）
        cache_service = context.getProperty('Distributed Cache Service').asControllerService()
        
        # 批量获取配置
        device_info = cache_service.get(f"device:{dev_id}", session)
        if device_info:
            device_data = json.loads(device_info)
            dev_group_id = device_data.get('dev_group_id')
            dev_product_id = device_data.get('dev_product_id')
            
            # 并行获取其他配置
            group_info = cache_service.get(f"group:{dev_group_id}", session)
            product_info = cache_service.get(f"product:{dev_product_id}", session)
            
            # 组装完整配置
            full_config = {
                'device': device_data,
                'group': json.loads(group_info) if group_info else {},
                'product': json.loads(product_info) if product_info else {}
            }
            
            # 存入本地缓存
            global_cache[cache_key] = full_config
            cache_timestamps[cache_key] = current_time
            
            return apply_config_to_flowfile(flowFile, session, full_config)
        
        return None
        
    except Exception as e:
        session.log.error(f"处理设备消息失败: {e}")
        return None

def apply_config_to_flowfile(flowFile, session, config):
    """将配置应用到FlowFile"""
    device_data = config.get('device', {})
    group_data = config.get('group', {})
    product_data = config.get('product', {})
    
    # 设置设备属性
    flowFile = session.putAttribute(flowFile, 'dev_name', device_data.get('dev_name', ''))
    flowFile = session.putAttribute(flowFile, 'dev_group_id', device_data.get('dev_group_id', ''))
    flowFile = session.putAttribute(flowFile, 'dev_product_id', device_data.get('dev_product_id', ''))
    flowFile = session.putAttribute(flowFile, 'dev_group_name', group_data.get('dev_group_name', ''))
    flowFile = session.putAttribute(flowFile, 'prop_items', json.dumps(product_data.get('prop_items', {})))
    
    return flowFile

# 主处理逻辑
flowFile = session.get()
if flowFile is not None:
    result = process_device_message(flowFile, session)
    if result:
        session.transfer(result, REL_SUCCESS)
    else:
        session.transfer(flowFile, REL_FAILURE)
'''

if __name__ == "__main__":
    # 示例使用
    redis_config = {
        'host': 'localhost',
        'port': 6379,
        'db': 0,
        'max_connections': 20
    }
    
    # 创建缓存管理器
    cache_manager = DeviceConfigCache(redis_config)
    
    # 预加载常用设备配置
    common_devices = ['D001', 'D002', 'D003']
    cache_manager.preload_configs(common_devices)
    
    # 获取缓存统计
    stats = cache_manager.get_cache_stats()
    print(f"缓存统计: {json.dumps(stats, indent=2, ensure_ascii=False)}")
    
    # 测试获取配置
    config = cache_manager.get_device_config('D001')
    if config:
        print(f"设备D001配置: {json.dumps(config, indent=2, ensure_ascii=False)}")
