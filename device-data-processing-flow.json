{"flowContents": {"identifier": "device-data-processing-flow", "name": "设备管理数据接入处理流程", "description": "从Kafka消费设备监测信息，查询Redis配置，写入PostgreSQL和TDengine数据库", "position": {"x": 0, "y": 0}, "processGroups": [], "remoteProcessGroups": [], "processors": [{"identifier": "kafka-consumer", "name": "ConsumeKafka_2_6", "type": "org.apache.nifi.processors.kafka.pubsub.ConsumeKafka_2_6", "position": {"x": 100, "y": 100}, "config": {"properties": {"bootstrap.servers": "localhost:9092", "topic": "device-monitoring", "group.id": "nifi-device-consumer", "auto.offset.reset": "latest", "security.protocol": "SASL_PLAINTEXT", "sasl.mechanism": "PLAIN", "sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"admin\" password=\"adminpassword\";", "message-demarcator": "\\n", "honor-transactions": "true", "max.poll.records": "10000"}, "schedulingPeriod": "1 sec", "schedulingStrategy": "TIMER_DRIVEN", "executionNode": "ALL", "penaltyDuration": "30 sec", "yieldDuration": "1 sec", "bulletinLevel": "WARN", "runDurationMillis": 0, "concurrentlySchedulableTaskCount": 1}}, {"identifier": "extract-json-fields", "name": "Evaluate<PERSON>sonPath", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 400, "y": 100}, "config": {"properties": {"Destination": "flowfile-attribute", "Return Type": "auto-detect", "Path Not Found Behavior": "warn", "Null Value Representation": "empty string", "ts": "$.ts", "dev_id": "$.dev_id", "factory_id": "$.factory_id", "prop_a": "$.prop_a", "prop_b": "$.prop_b", "prop_c": "$.prop_c", "prop_d": "$.prop_d"}}}, {"identifier": "fetch-device-info", "name": "ExecuteScript - 查询设备信息", "type": "org.apache.nifi.processors.script.ExecuteScript", "position": {"x": 700, "y": 50}, "config": {"properties": {"Script Engine": "python", "Script Body": "import json\nimport redis\n\n# Redis连接配置\nr = redis.Redis(host='localhost', port=6379, db=0)\n\n# 获取FlowFile\nflowFile = session.get()\nif flowFile is not None:\n    dev_id = flowFile.getAttribute('dev_id')\n    \n    # 从Redis查询设备信息\n    device_info_key = f'device:{dev_id}'\n    device_info = r.get(device_info_key)\n    \n    if device_info:\n        device_data = json.loads(device_info)\n        flowFile = session.putAttribute(flowFile, 'dev_name', device_data.get('dev_name', ''))\n        flowFile = session.putAttribute(flowFile, 'dev_group_id', device_data.get('dev_group_id', ''))\n        flowFile = session.putAttribute(flowFile, 'dev_product_id', device_data.get('dev_product_id', ''))\n        \n    session.transfer(flowFile, REL_SUCCESS)\nelse:\n    session.transfer(flowFile, REL_FAILURE)"}}}, {"identifier": "fetch-group-info", "name": "ExecuteScript - 查询设备分组信息", "type": "org.apache.nifi.processors.script.ExecuteScript", "position": {"x": 700, "y": 150}, "config": {"properties": {"Script Engine": "python", "Script Body": "import json\nimport redis\n\n# Redis连接配置\nr = redis.Redis(host='localhost', port=6379, db=0)\n\n# 获取FlowFile\nflowFile = session.get()\nif flowFile is not None:\n    dev_group_id = flowFile.getAttribute('dev_group_id')\n    \n    # 从Redis查询设备分组信息\n    group_info_key = f'group:{dev_group_id}'\n    group_info = r.get(group_info_key)\n    \n    if group_info:\n        group_data = json.loads(group_info)\n        flowFile = session.putAttribute(flowFile, 'dev_group_name', group_data.get('dev_group_name', ''))\n        \n    session.transfer(flowFile, REL_SUCCESS)\nelse:\n    session.transfer(flowFile, REL_FAILURE)"}}}, {"identifier": "fetch-product-info", "name": "ExecuteScript - 查询设备属性信息", "type": "org.apache.nifi.processors.script.ExecuteScript", "position": {"x": 700, "y": 250}, "config": {"properties": {"Script Engine": "python", "Script Body": "import json\nimport redis\n\n# Redis连接配置\nr = redis.Redis(host='localhost', port=6379, db=0)\n\n# 获取FlowFile\nflowFile = session.get()\nif flowFile is not None:\n    dev_product_id = flowFile.getAttribute('dev_product_id')\n    \n    # 从Redis查询设备属性信息\n    product_info_key = f'product:{dev_product_id}'\n    product_info = r.get(product_info_key)\n    \n    if product_info:\n        product_data = json.loads(product_info)\n        flowFile = session.putAttribute(flowFile, 'prop_items', json.dumps(product_data.get('prop_items', {})))\n        \n    session.transfer(flowFile, REL_SUCCESS)\nelse:\n    session.transfer(flowFile, REL_FAILURE)"}}}], "inputPorts": [], "outputPorts": [], "connections": [{"identifier": "kafka-to-json", "name": "", "source": {"id": "kafka-consumer", "groupId": "root", "name": "ConsumeKafka_2_6", "type": "PROCESSOR"}, "destination": {"id": "extract-json-fields", "groupId": "root", "name": "Evaluate<PERSON>sonPath", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "json-to-device", "name": "", "source": {"id": "extract-json-fields", "groupId": "root", "name": "Evaluate<PERSON>sonPath", "type": "PROCESSOR"}, "destination": {"id": "fetch-device-info", "groupId": "root", "name": "ExecuteScript - 查询设备信息", "type": "PROCESSOR"}, "selectedRelationships": ["matched"], "availableRelationships": ["matched", "unmatched", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "device-to-group", "name": "", "source": {"id": "fetch-device-info", "groupId": "root", "name": "ExecuteScript - 查询设备信息", "type": "PROCESSOR"}, "destination": {"id": "fetch-group-info", "groupId": "root", "name": "ExecuteScript - 查询设备分组信息", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "group-to-product", "name": "", "source": {"id": "fetch-group-info", "groupId": "root", "name": "ExecuteScript - 查询设备分组信息", "type": "PROCESSOR"}, "destination": {"id": "fetch-product-info", "groupId": "root", "name": "ExecuteScript - 查询设备属性信息", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}], "labels": [], "funnels": [], "controllerServices": []}}