{"flowContents": {"identifier": "device-data-processing-flow", "name": "设备管理数据接入处理流程", "description": "从Kafka消费设备监测信息，查询Redis配置，写入PostgreSQL和TDengine数据库", "position": {"x": 0, "y": 0}, "processGroups": [], "remoteProcessGroups": [], "processors": [{"identifier": "kafka-consumer", "name": "ConsumeKafka_2_6", "type": "org.apache.nifi.processors.kafka.pubsub.ConsumeKafka_2_6", "position": {"x": 100, "y": 100}, "config": {"properties": {"bootstrap.servers": "localhost:9092", "topic": "device-monitoring", "group.id": "nifi-device-consumer", "auto.offset.reset": "latest", "security.protocol": "SASL_PLAINTEXT", "sasl.mechanism": "PLAIN", "sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"admin\" password=\"adminpassword\";", "message-demarcator": "\\n", "honor-transactions": "true", "max.poll.records": "10000"}, "schedulingPeriod": "1 sec", "schedulingStrategy": "TIMER_DRIVEN", "executionNode": "ALL", "penaltyDuration": "30 sec", "yieldDuration": "1 sec", "bulletinLevel": "WARN", "runDurationMillis": 0, "concurrentlySchedulableTaskCount": 1}}, {"identifier": "extract-json-fields", "name": "Evaluate<PERSON>sonPath", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 400, "y": 100}, "config": {"properties": {"Destination": "flowfile-attribute", "Return Type": "auto-detect", "Path Not Found Behavior": "warn", "Null Value Representation": "empty string", "ts": "$.ts", "dev_id": "$.dev_id", "factory_id": "$.factory_id", "prop_a": "$.prop_a", "prop_b": "$.prop_b", "prop_c": "$.prop_c", "prop_d": "$.prop_d"}}}, {"identifier": "fetch-device-info", "name": "FetchDistributedMapCache - 设备信息", "type": "org.apache.nifi.processors.standard.FetchDistributedMapCache", "position": {"x": 700, "y": 50}, "config": {"properties": {"Distributed Cache Service": "redis-distributed-cache", "Cache Entry Identifier": "device:${dev_id}", "Put Cache Value In Attribute": "device_info_json"}}}, {"identifier": "parse-device-info", "name": "EvaluateJsonPath - 解析设备信息", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 700, "y": 150}, "config": {"properties": {"Destination": "flowfile-attribute", "Return Type": "auto-detect", "Path Not Found Behavior": "warn", "dev_name": "$.dev_name", "dev_group_id": "$.dev_group_id", "dev_product_id": "$.dev_product_id"}, "autoTerminatedRelationships": ["unmatched"]}}, {"identifier": "fetch-group-info", "name": "FetchDistributedMapCache - 分组信息", "type": "org.apache.nifi.processors.standard.FetchDistributedMapCache", "position": {"x": 1000, "y": 100}, "config": {"properties": {"Distributed Cache Service": "redis-distributed-cache", "Cache Entry Identifier": "group:${dev_group_id}", "Put Cache Value In Attribute": "group_info_json"}}}, {"identifier": "fetch-product-info", "name": "FetchDistributedMapCache - 产品信息", "type": "org.apache.nifi.processors.standard.FetchDistributedMapCache", "position": {"x": 1000, "y": 200}, "config": {"properties": {"Distributed Cache Service": "redis-distributed-cache", "Cache Entry Identifier": "product:${dev_product_id}", "Put Cache Value In Attribute": "product_info_json"}}}, {"identifier": "parse-group-info", "name": "EvaluateJsonPath - 解析分组信息", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 1300, "y": 100}, "config": {"properties": {"Destination": "flowfile-attribute", "Return Type": "auto-detect", "Path Not Found Behavior": "warn", "dev_group_name": "$.dev_group_name"}, "autoTerminatedRelationships": ["unmatched"]}}, {"identifier": "parse-product-info", "name": "EvaluateJsonPath - 解析产品信息", "type": "org.apache.nifi.processors.standard.EvaluateJsonPath", "position": {"x": 1300, "y": 200}, "config": {"properties": {"Destination": "flowfile-attribute", "Return Type": "auto-detect", "Path Not Found Behavior": "warn", "prop_items": "$.prop_items"}, "autoTerminatedRelationships": ["unmatched"]}}, {"identifier": "split-properties", "name": "ExecuteScript - 拆分属性数据", "type": "org.apache.nifi.processors.script.ExecuteScript", "position": {"x": 1000, "y": 200}, "config": {"properties": {"Script Engine": "python", "Script Body": "import json\n\n# 获取FlowFile\nflowFile = session.get()\nif flowFile is not None:\n    dev_id = flowFile.getAttribute('dev_id')\n    dev_name = flowFile.getAttribute('dev_name')\n    factory_id = flowFile.getAttribute('factory_id')\n    dev_group_id = flowFile.getAttribute('dev_group_id')\n    ts = flowFile.getAttribute('ts')\n    prop_items_str = flowFile.getAttribute('prop_items')\n    \n    if prop_items_str:\n        prop_items = json.loads(prop_items_str)\n        \n        # 为每个属性创建单独的FlowFile\n        for prop_key, prop_info in prop_items.items():\n            prop_value = flowFile.getAttribute(prop_key)\n            if prop_value is not None:\n                # 生成关键信息\n                point_id = f\"{dev_id}_{prop_info['prop_id']}\"\n                point_name = f\"{dev_name}_{prop_info['prop_name']}\"\n                system_id = f\"001_{dev_group_id}\"\n                \n                # 创建新的FlowFile\n                newFlowFile = session.create(flowFile)\n                newFlowFile = session.putAttribute(newFlowFile, 'point_id', point_id)\n                newFlowFile = session.putAttribute(newFlowFile, 'point_name', point_name)\n                newFlowFile = session.putAttribute(newFlowFile, 'system_id', system_id)\n                newFlowFile = session.putAttribute(newFlowFile, 'factory_id', factory_id)\n                newFlowFile = session.putAttribute(newFlowFile, 'data_type', prop_info['data_type'])\n                newFlowFile = session.putAttribute(newFlowFile, 'ts', ts)\n                newFlowFile = session.putAttribute(newFlowFile, 'value', str(prop_value))\n                \n                session.transfer(newFlowFile, REL_SUCCESS)\n    \n    session.remove(flowFile)\nelse:\n    session.transfer(flowFile, REL_FAILURE)"}}}, {"identifier": "insert-point-definition", "name": "PutSQL - 插入点位定义", "type": "org.apache.nifi.processors.standard.PutSQL", "position": {"x": 1300, "y": 100}, "config": {"properties": {"JDBC Connection Pool": "postgresql-connection-pool", "sql.args.1.type": "12", "sql.args.1.value": "${point_id}", "sql.args.2.type": "12", "sql.args.2.value": "${point_name}", "sql.args.3.type": "12", "sql.args.3.value": "${factory_id}", "sql.args.4.type": "12", "sql.args.4.value": "${system_id}", "sql.args.5.type": "12", "sql.args.5.value": "${data_type}", "Support Fragmented Transactions": "true", "Transaction Timeout": "0 seconds", "Batch Size": "100"}}}, {"identifier": "insert-realtime-data", "name": "PutSQL - 插入实时数据", "type": "org.apache.nifi.processors.standard.PutSQL", "position": {"x": 1300, "y": 200}, "config": {"properties": {"JDBC Connection Pool": "postgresql-connection-pool", "sql.args.1.type": "12", "sql.args.1.value": "${point_id}", "sql.args.2.type": "93", "sql.args.2.value": "${ts}", "sql.args.3.type": "12", "sql.args.3.value": "${value}", "Support Fragmented Transactions": "true", "Transaction Timeout": "0 seconds", "Batch Size": "100"}}}, {"identifier": "insert-tdengine-data", "name": "ExecuteSQL - 插入TDengine历史数据", "type": "org.apache.nifi.processors.standard.ExecuteSQL", "position": {"x": 1300, "y": 300}, "config": {"properties": {"Database Connection Pooling Service": "tdengine-connection-pool", "SQL select query": "INSERT INTO device_history USING device_history_template TAGS ('${point_id}', '${point_name}', '${factory_id}', '${system_id}', '${data_type}') VALUES ('${ts}', '${value}')", "Max Wait Time": "0 seconds", "Normalize Table/Column Names": "false"}}}, {"identifier": "generate-point-definition-sql", "name": "ReplaceText - 生成点位定义SQL", "type": "org.apache.nifi.processors.standard.ReplaceText", "position": {"x": 1000, "y": 100}, "config": {"properties": {"Search Value": ".*", "Replacement Value": "INSERT INTO device_point_definition (point_id, point_name, factory_id, system_id, data_type) VALUES (?, ?, ?, ?, ?) ON CONFLICT (point_id) DO NOTHING", "Replacement Strategy": "Regex Replace", "Evaluation Mode": "Entire text"}}}, {"identifier": "generate-realtime-data-sql", "name": "ReplaceText - 生成实时数据SQL", "type": "org.apache.nifi.processors.standard.ReplaceText", "position": {"x": 1000, "y": 300}, "config": {"properties": {"Search Value": ".*", "Replacement Value": "INSERT INTO device_realtime_data (point_id, ts, value) VALUES (?, ?, ?) ON CONFLICT (point_id) DO UPDATE SET ts = EXCLUDED.ts, value = EXCLUDED.value", "Replacement Strategy": "Regex Replace", "Evaluation Mode": "Entire text"}}}], "inputPorts": [], "outputPorts": [], "connections": [{"identifier": "kafka-to-json", "name": "", "source": {"id": "kafka-consumer", "groupId": "root", "name": "ConsumeKafka_2_6", "type": "PROCESSOR"}, "destination": {"id": "extract-json-fields", "groupId": "root", "name": "Evaluate<PERSON>sonPath", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "json-to-device", "name": "", "source": {"id": "extract-json-fields", "groupId": "root", "name": "Evaluate<PERSON>sonPath", "type": "PROCESSOR"}, "destination": {"id": "fetch-device-info", "groupId": "root", "name": "FetchDistributedMapCache - 设备信息", "type": "PROCESSOR"}, "selectedRelationships": ["matched"], "availableRelationships": ["matched", "unmatched", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "device-to-parse", "name": "", "source": {"id": "fetch-device-info", "groupId": "root", "name": "FetchDistributedMapCache - 设备信息", "type": "PROCESSOR"}, "destination": {"id": "parse-device-info", "groupId": "root", "name": "EvaluateJsonPath - 解析设备信息", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "parse-to-group", "name": "", "source": {"id": "parse-device-info", "groupId": "root", "name": "EvaluateJsonPath - 解析设备信息", "type": "PROCESSOR"}, "destination": {"id": "fetch-group-info", "groupId": "root", "name": "FetchDistributedMapCache - 分组信息", "type": "PROCESSOR"}, "selectedRelationships": ["matched"], "availableRelationships": ["matched", "unmatched", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "parse-to-product", "name": "", "source": {"id": "parse-device-info", "groupId": "root", "name": "EvaluateJsonPath - 解析设备信息", "type": "PROCESSOR"}, "destination": {"id": "fetch-product-info", "groupId": "root", "name": "FetchDistributedMapCache - 产品信息", "type": "PROCESSOR"}, "selectedRelationships": ["matched"], "availableRelationships": ["matched", "unmatched", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "group-to-product", "name": "", "source": {"id": "fetch-group-info", "groupId": "root", "name": "ExecuteScript - 查询设备分组信息", "type": "PROCESSOR"}, "destination": {"id": "fetch-product-info", "groupId": "root", "name": "ExecuteScript - 查询设备属性信息", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "product-to-split", "name": "", "source": {"id": "fetch-product-info", "groupId": "root", "name": "ExecuteScript - 查询设备属性信息", "type": "PROCESSOR"}, "destination": {"id": "split-properties", "groupId": "root", "name": "ExecuteScript - 拆分属性数据", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "split-to-definition-sql", "name": "", "source": {"id": "split-properties", "groupId": "root", "name": "ExecuteScript - 拆分属性数据", "type": "PROCESSOR"}, "destination": {"id": "generate-point-definition-sql", "groupId": "root", "name": "ReplaceText - 生成点位定义SQL", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "split-to-realtime-sql", "name": "", "source": {"id": "split-properties", "groupId": "root", "name": "ExecuteScript - 拆分属性数据", "type": "PROCESSOR"}, "destination": {"id": "generate-realtime-data-sql", "groupId": "root", "name": "ReplaceText - 生成实时数据SQL", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "definition-sql-to-insert", "name": "", "source": {"id": "generate-point-definition-sql", "groupId": "root", "name": "ReplaceText - 生成点位定义SQL", "type": "PROCESSOR"}, "destination": {"id": "insert-point-definition", "groupId": "root", "name": "PutSQL - 插入点位定义", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "realtime-sql-to-insert", "name": "", "source": {"id": "generate-realtime-data-sql", "groupId": "root", "name": "ReplaceText - 生成实时数据SQL", "type": "PROCESSOR"}, "destination": {"id": "insert-realtime-data", "groupId": "root", "name": "PutSQL - 插入实时数据", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}, {"identifier": "split-to-tdengine", "name": "", "source": {"id": "split-properties", "groupId": "root", "name": "ExecuteScript - 拆分属性数据", "type": "PROCESSOR"}, "destination": {"id": "insert-tdengine-data", "groupId": "root", "name": "ExecuteSQL - 插入TDengine历史数据", "type": "PROCESSOR"}, "selectedRelationships": ["success"], "availableRelationships": ["success", "failure"], "backPressureObjectThreshold": 10000, "backPressureDataSizeThreshold": "1 GB", "flowFileExpiration": "0 sec", "prioritizers": [], "bends": [], "labelIndex": 1, "zIndex": 0}], "labels": [], "funnels": [], "controllerServices": [{"identifier": "redis-distributed-cache", "name": "RedisDistributedMapCacheClientService", "type": "org.apache.nifi.redis.service.RedisDistributedMapCacheClientService", "properties": {"Redis Connection Pool": "redis-connection-pool", "Communications Timeout": "30 secs"}}, {"identifier": "redis-connection-pool", "name": "RedisConnectionPoolService", "type": "org.apache.nifi.redis.service.RedisConnectionPoolService", "properties": {"Connection String": "localhost:6379", "Database Index": "0", "Communication Timeout": "10 secs", "Pool - Max Total": "20", "Pool - Max Idle": "10", "Pool - Min Idle": "2", "Pool - Block When Exhausted": "true", "Pool - Max Wait Time": "10 secs", "Pool - Min Evictable Idle Time": "60 secs", "Pool - Time Between Eviction Runs": "30 secs"}}, {"identifier": "postgresql-connection-pool", "name": "DBCPConnectionPool - PostgreSQL", "type": "org.apache.nifi.dbcp.DBCPConnectionPool", "properties": {"Database Connection URL": "**************************************************", "Database Driver Class Name": "org.postgresql.Driver", "Database Driver Location(s)": "/opt/nifi/nifi-current/lib/postgresql-42.7.0.jar", "Database User": "postgres", "Password": "password", "Max Wait Time": "500 millis", "Max Total Connections": "20", "Max Idle Connections": "10", "Min Idle Connections": "2", "Validation Query": "SELECT 1"}}, {"identifier": "tdengine-connection-pool", "name": "DBCPConnectionPool - TDengine", "type": "org.apache.nifi.dbcp.DBCPConnectionPool", "properties": {"Database Connection URL": "********************************************", "Database Driver Class Name": "com.taosdata.jdbc.TSDBDriver", "Database Driver Location(s)": "/opt/nifi/nifi-current/lib/taos-jdbcdriver-3.2.0.jar", "Database User": "root", "Password": "taosdata", "Max Wait Time": "500 millis", "Max Total Connections": "8", "Validation Query": "SELECT SERVER_VERSION()"}}]}}