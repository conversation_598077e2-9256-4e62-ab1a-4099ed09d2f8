使用 Apache Nifi 和 Registry 开发 Kafka 消费处理程序，版本号都是 2.4.0。
 
消费处理的大致逻辑是：从 Kafka 消费设备的各类监测信息，然后将数据拆分按传感器监测点位写入 PostgreSQL 和 TDengine 数据库中。

Kafka 消息格式示例如下：
{
  ts: '2023-01-01 00:00:00',
  dev_id: 'D001',
  factory_id: 'F001',
  prop_a: '测试数据1',
  prop_b: 12.3545,
  prop_c: 1,
  prop_d: false
}

设备监测信息解析需要用到基础配置信息，此类信息初步计划放在  Redis 中（可以建议进行优化），其中：
1）设备信息，示例如下：
{
  dev_id: 'D001',
  dev_name: '设备1',
  dev_group_id: 'G001',
  dev_product_id: 'P001'
}

2）设备分组信息：
{
  dev_group_id: 'G001',
  dev_group_name: '设备分组1'
}

3）设备属性信息：
{
  dev_product_id: 'P001',
  dev_product_name: '设备产品1',
  prop_items: {
    prop_a: { prop_id: 'A001', prop_name: '文本属性a', data_type: 'string' },
    prop_b: { prop_id: 'B001', prop_name: '浮点型属性b', data_type: 'double' }
    prop_c: { prop_id: 'C001', prop_name: '整型属性c', data_type: 'int' }
    prop_d: { prop_id: 'D001', prop_name: '布尔型属性d', data_type: 'boolean' }
  }
}

需要根据现有信息生成的关键信息有：
点位ID(point_id): <dev_id>_<prop_id>, 比如 "D001_A001"
点位名称(point_name): <dev_name>_<prop_name>, 比如 "设备1_文本属性a"
系统ID(system_id): 001_<dev_group_id>, 001 为固定值，比如 "001_G001"

PostgreSQL 数据库写入的表有：

设备点位定义表字段示例：point_id, point_name, factory_id, system_id, data_type，例如：D001_A001, 设备1_文本属性a, F001, 001_G001, string
设备点位实时数据表字段示例：point_id, ts, value，比如：D001_A001, 2023-01-01 00:00:00, 测试数据1

TDengine 数据库写入的表有：
设备点位历史数据表，包含：
1）普通列：ts, value
2）标签列：point_id, point_name, factory_id, system_id, data_type